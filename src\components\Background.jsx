import { useEffect, useRef } from 'react'

function Background({ children }) {
  const splineContainerRef = useRef(null)

  useEffect(() => {
    const container = splineContainerRef.current
    if (!container) return

    // Create spline-viewer element
    const splineViewer = document.createElement('spline-viewer')
    splineViewer.setAttribute('url', 'https://prod.spline.design/perCAIenhEw2tEZc/scene.splinecode')
    splineViewer.style.width = '100%'
    splineViewer.style.height = '100%'
    splineViewer.style.display = 'block'
    splineViewer.style.background = 'transparent'

    // Add to container
    container.appendChild(splineViewer)

    // Cleanup function
    return () => {
      if (container.contains(splineViewer)) {
        container.removeChild(splineViewer)
      }
    }
  }, [])

  return (
    <div className="min-h-screen bg-black flex items-center justify-start relative overflow-hidden">
      {/* Spline 3D Background */}
      <div
        ref={splineContainerRef}
        className="absolute inset-0 z-0"
        style={{ width: '100%', height: '100%' }}
      />

      {/* Dark overlay to ensure text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-30 z-5"></div>

      {/* Enhanced Dark Background Gradient for fallback */}
      <div className="absolute inset-0 bg-gradient-radial from-gray-900 via-black to-black opacity-20 z-5"></div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default Background
