function Background({ children }) {
  return (
    <div className="min-h-screen bg-black flex items-center justify-start relative overflow-hidden">
      {/* Spline 3D Background */}
      <div className="absolute inset-0 z-0">
        <spline-viewer
          url="https://prod.spline.design/perCAIenhEw2tEZc/scene.splinecode"
          style={{
            width: '100%',
            height: '100%',
            background: 'transparent'
          }}
        />
      </div>

      {/* Dark overlay to ensure text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-40 z-5"></div>

      {/* Enhanced Dark Background Gradient for fallback */}
      <div className="absolute inset-0 bg-gradient-radial from-gray-900 via-black to-black opacity-20 z-5"></div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default Background
